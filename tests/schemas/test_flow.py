from pathlib import Path

from s3path import S3Path

from app.schemas.flows import FileOutput, MessageOutput
from app.utils.app_config import app_config

_output_file_path = 'STOA2507/SZ-STOA25075002.xlsx'

def test_file_output():
    file_path = app_config.cache_dir / _output_file_path
    x = FileOutput(file_path = file_path)
    assert isinstance(x.file_path, Path)
    assert str(x.file_path) == str(file_path)
    print(x.model_dump_json())

def test_file_output2():
    x = FileOutput.model_validate({'file_path': _output_file_path})
    assert isinstance(x.file_path, Path)
    assert str(x.file_path) == str(app_config.cache_dir / _output_file_path)
    print(x.model_dump_json())

def test_file_output_s3():
    file_path = f'/{app_config.cache_bucket}/SZ-STOA2507/SZ-STOA25072062.xlsx'
    x = FileOutput.model_validate({'file_path': file_path, 'remote': True})
    assert isinstance(x.file_path, S3Path)
    assert str(x.file_path) == str(file_path)
    print(x.model_dump_json())

def test_message_output():
    x = MessageOutput(message='Hello World')
    print(x.model_dump_json())

    x = MessageOutput(progress=50, message='Hello World')
    print(x.model_dump_json())
