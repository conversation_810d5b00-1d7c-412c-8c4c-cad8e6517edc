import logging
from pathlib import Path
from dotenv import load_dotenv
import pytest


def pytest_configure(config):
    logging.basicConfig(level=logging.INFO)

def pytest_sessionstart(session: pytest.Session) -> None:
    project_dir = Path(__file__).parent.parent

    """Load env variables before tests run"""
    env_path = project_dir / '.env'
    load_dotenv(env_path, override=True)
    print(f"\nLoaded environment variables from {env_path}")

    print(logging.getLogger().level)
