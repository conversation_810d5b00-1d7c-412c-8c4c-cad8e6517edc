import pytest

from app.database.redis_client import del_pattern, redis_client, get_credential_key, set_val, get_val, del_val, mget_val
from app.schemas.common import VendorTypes

@pytest.mark.asyncio
async def test_mget():
    _to_addrs_key = get_credential_key(VendorTypes.General, 'wms-receiver')
    _cc_addrs_key = get_credential_key(VendorTypes.ChangCheng004, 'emails')
    emails = await mget_val(_to_addrs_key, _cc_addrs_key)
    assert emails == ['<EMAIL>', '<EMAIL>']

@pytest.mark.asyncio
async def test_del_pattern():
    await redis_client.set('cred:Test:1', '1')
    await redis_client.set('cred:Test:2', '2')
    deleted_count = await del_pattern('cred:Test:*')
    assert deleted_count == 2


@pytest.mark.asyncio
async def test_credential():
    # test set & get
    cred_key = get_credential_key(VendorTypes.KingDee, 'test')
    await set_val(cred_key, 'test')
    credential = await get_val(cred_key)
    assert credential == 'test'

    # test delete
    await del_val(cred_key)
    cred = await get_val(cred_key)
    assert cred is None


@pytest.mark.asyncio
async def test_credential_add_wms_receiver():
    await set_val(get_credential_key(VendorTypes.General, 'wms-receiver'), '<EMAIL>')


@pytest.mark.asyncio
async def test_credential_add_changcheng_emails():
    await set_val(get_credential_key(VendorTypes.ChangCheng004, 'emails'), '<EMAIL>')
