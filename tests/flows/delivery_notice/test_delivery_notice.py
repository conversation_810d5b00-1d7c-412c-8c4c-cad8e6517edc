import pytest

from app.flows.delivery_notice.delivery_notice_strategy import DeliveryNoticeFactory
from app.routers.flows import get_outstock_info

excel_nos = [
    # 'SZ-STOA25075002', # 001_qiyi
    # 'XY-STOA25070058',
    # 'SZ-STOA25074282', # 002_tianli
    # 'SZ-STOA25070802', # 003_wankezi
    # 'SZ-STOA25074846', # 004_changecheng
    # 'SZ-STOA25073704', # 005_xingqudongli
]

@pytest.mark.asyncio
@pytest.mark.parametrize('outstock_no', excel_nos)
async def test_generate_excel(outstock_no):
    outstock_json = await get_outstock_info(outstock_no)
    strategy = DeliveryNoticeFactory.get_strategy(outstock_json, {})
    res = await strategy.get_excel()
    assert res.file_path.exists()

merged_outstocknos = [
    # ['SZ-STOA25071692', 'SZ-STOA25071691', 'SZ-STOA25073368', 'SZ-STOA25073308'], # 006_zhiwei
    ['SZ-STOA25072915', 'SZ-STOA25072924','SZ-STOA25073112'], # 007_meige
]

@pytest.mark.asyncio
@pytest.mark.parametrize('outstock_nos', merged_outstocknos)
async def test_generate_merged_excel(outstock_nos):
    dfs = []
    strategy = None
    for outstock_no in outstock_nos:
        outstock_json = await get_outstock_info(outstock_no)
        strategy = DeliveryNoticeFactory.get_strategy(outstock_json, {})
        df = await strategy.get_current_df()
        dfs.append(df)
    strategy.dfs = dfs
    res = await strategy.get_excel()
    assert res.file_path.exists()