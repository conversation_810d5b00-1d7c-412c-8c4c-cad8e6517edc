from pathlib import Path

import pytest



@pytest.mark.asyncio
async def test_send_email():
    from app.utils.email_util import send_email

    await send_email(
        to_addrs='<EMAIL>',
        subject= f'长城送货单 {'xx2'}',
        body = f"""
    <html>
  <body>
    <p>{'发货通知单号'} {'客户名称'}</p>
  </body>
</html>
""",
        attachments=[Path('/Users/<USER>/code/fast-flow/.venv/jcache/SZ-STOA2507/SZ-STOA25074332_no.pdf')])