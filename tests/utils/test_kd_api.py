import asyncio

from app.utils.kingd_api import kd_api


async def run():
    res = await kd_api.post('/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.View.common.kdsvc', json={
        "data": {
            "CreateOrgId": 0,
            "Number": "SZ-STOA25074027",
            "Id": ""
        },
        "formid": "SAL_DELIVERYNOTICE"
    }, extensions={'uid': '周雪玲'})
    print(11, res.json())

if __name__ == '__main__':
    asyncio.run(run())