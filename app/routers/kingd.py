
from fastapi import APIRouter, HTTPException, Body
from pydantic import BaseModel

from app.utils.kingd_api import kd_api
from app.utils.kingd_util import KDFilterCondition

router = APIRouter()


class KDViewModel(BaseModel):
    CreateOrgId: int = 0
    Number: str
    Id: str = ''

@router.post('/view')
async def form_view(
    data: KDViewModel,
    formid: str = Body(
        examples=[
            "SAL_DELIVERYNOTICE",
            "SAL_SaleOrder",
            "BD_MATERIAL",
            "BD_Customer",
            "BD_Supplier"
            "BD_Empinfo"
        ]
    )
):
    res = await kd_api.post('/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.View.common.kdsvc', json = {
        'formid': formid,
        'data': data.model_dump()
    }, extensions={'uid': '周雪玲'})
    output = res.json()
    if not output['Result']['ResponseStatus']['IsSuccess']:
        print(output)
        raise HTTPException(status_code=400, detail=output['Result']['ResponseStatus']['Errors'])
    return output['Result']['Result']


class KDSaveModel(BaseModel, extra = 'allow'):
    FID: int
@router.post('/save')
async def form_save(
    model: KDSaveModel,
    formid: str = Body(
        examples=[
            "SAL_DELIVERYNOTICE"
        ]
    )
):
    res = await kd_api.post('/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Save.common.kdsvc', json = {
        'formid': formid,
        'data': {
            "ValidateFlag": False,
            'model': model.model_dump()
        }
    }, extensions={'uid': '周雪玲'})
    output = res.json()
    if not output['Result']['ResponseStatus']['IsSuccess']:
        print(output)
        raise HTTPException(status_code=400, detail=output['Result']['ResponseStatus']['Errors'])
    return output['Result']



class KDQueryModel(BaseModel, extra = 'allow'):
    FormId: str
    FieldKeys: str
    FilterString: list[KDFilterCondition]
    Limit: int = None
@router.post('/query')
async def form_query(model: KDQueryModel):
    res = await kd_api.post('/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.BillQuery.common.kdsvc', json = {
        'data': model.model_dump()
    }, extensions={'uid': '周雪玲'})
    output = res.json()
    return output