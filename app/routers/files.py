import hashlib
import os
import uuid
from datetime import datetime
from pathlib import Path
from urllib.parse import unquote

from fastapi import UploadFile, APIRouter, Request, HTTPException
from starlette.responses import FileResponse

from app.schemas.flows import FileOutput
from app.utils.file_util import ensure_dir_for_file

router = APIRouter()

from app.utils.app_config import app_config

_tmp_dir = app_config.cache_dir / 'tmp'
ensure_dir_for_file(_tmp_dir / 'xx')

@router.post("/")
async def upload_file(request: Request):
    filename = unquote(request.headers.get('filename', ''))
    tmp_path =  _tmp_dir / str(uuid.uuid4())
    hash_func = hashlib.new('sha256')
    with open(tmp_path, "wb") as f:
        async for chunk in request.stream():
            f.write(chunk)
            hash_func.update(chunk)
    file_key = f"{datetime.now().strftime('%Y-%m')}/{hash_func.hexdigest()}{Path(filename).suffix.lower()}"
    new_path = app_config.cache_dir / file_key
    ensure_dir_for_file(new_path)
    os.rename(tmp_path, new_path)
    return FileOutput(file_path=new_path, filename=filename)

@router.get("/{file_key:path}")
async def get_file(file_key: str):
    file_path = app_config.cache_dir / file_key
    if not file_path.exists():
        raise HTTPException(status_code=404, detail="File not found")
    return FileResponse(file_path)