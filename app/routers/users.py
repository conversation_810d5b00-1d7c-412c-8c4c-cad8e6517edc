
from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel

from app.database.redis_client import set_val, get_credential_key, get_cache_key, CacheTypes
from app.schemas.common import GeneralCredential, VendorTypes
from app.utils.kingd_api import login_by_password, login_by_sign
from app.utils.auth_util import (
    create_access_token, create_refresh_token, verify_token,
    get_current_user, UserInfo, ACCESS_TOKEN_EXPIRE_MINUTES
)

router = APIRouter()


class LoginRequest(BaseModel):
    username: str
    password: str


class TokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    expires_in: int


class RefreshTokenRequest(BaseModel):
    refresh_token: str


# https://n8n.17ch.cn/webhook/get-user?user_id=4427689
async def verify_user(name: str, password: str = None, check_only = False):
    """验证用户凭据"""
    if check_only:
        res_json = await login_by_sign(name)
    else:
        res_json = await login_by_password(name, password)
    session_id = res_json.get('KDSVCSessionId')
    if not session_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=res_json.get('Message', '登录出错！')
        )
    return {
        "session_id": session_id,
        "UserId": res_json['Context']['UserId'],
        "UserName": res_json['Context']['UserName'],
    }

async def get_token_response(user_info: dict):
    name = user_info['UserName']

    token_cache_key = get_cache_key(name, cache_type=CacheTypes.Token, vendor = VendorTypes.KingDee)
    await set_val(token_cache_key, user_info['session_id'])

    data = {
        "user_id": user_info['UserId'],
        "name": name,
    }
    access_token = create_access_token(data)
    refresh_token = create_refresh_token(data)

    return TokenResponse(
        access_token=access_token,
        refresh_token=refresh_token,
        expires_in=ACCESS_TOKEN_EXPIRE_MINUTES * 60
    )

@router.post("/login", response_model=TokenResponse)
async def login_for_token(login_data: LoginRequest):
    """用户登录获取token"""
    try:
        user_info = await verify_user(login_data.username, login_data.password)
        return await get_token_response(user_info)
    except HTTPException as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"登录失败: {e.detail}"
        )


@router.post("/refresh", response_model=TokenResponse)
async def post_refresh_token(refresh_data: RefreshTokenRequest):
    """刷新访问token"""
    payload = verify_token(refresh_data.refresh_token)

    user_info = await verify_user(payload.get('name'), check_only=True)
    return await get_token_response(user_info)


@router.get("/me", response_model=UserInfo)
async def get_current_user_info(current_user: UserInfo = Depends(get_current_user)):
    """获取当前用户信息"""
    return current_user


@router.post("/credential/{ctype}")
async def set_credential_by_user(ctype: VendorTypes, credential: GeneralCredential, current_user: UserInfo = Depends(get_current_user)):
    await set_val(get_credential_key(ctype, current_user.name), credential.model_dump())
    return {'status': 'ok'}

