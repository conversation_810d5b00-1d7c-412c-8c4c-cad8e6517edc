#!lua name=del_pattern

redis.register_function(
    'del_pattern',
    function(keys, args)
        local pattern = args[1]
        local batch_size = tonumber(args[2]) or 1000
        local delete_delay = tonumber(args[3]) or 0

        local cursor = 0
        local deleted = 0

        repeat
            local res = redis.call('SCAN', cursor, 'MATCH', pattern, 'COUNT', batch_size)
            cursor = tonumber(res[1])
            local keys = res[2]

            if #keys > 0 then
                redis.call('DEL', unpack(keys))
                deleted = deleted + #keys

                if delete_delay > 0 then
                    redis.call('SLEEP', delete_delay)
                end
            end
        until cursor == 0

        return deleted
    end
)