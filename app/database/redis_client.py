import enum
import functools
import json
from pathlib import Path

from redis.asyncio import Redis

from app.schemas.common import VendorTypes
from app.utils.app_config import app_config

import logging
logger = logging.getLogger(__name__)
# logger.setLevel(logging.DEBUG)

redis_client = Redis.from_url(
    str(app_config.redis_url),
    decode_responses=True,
    socket_connect_timeout=5,  # Timeout for initial connection
    socket_timeout=5,          # Timeout for individual commands
    retry_on_timeout = True,
)

async def install_function(func_name):
    function_path = Path(__file__).parent / f'{func_name}.lua'
    function_source = function_path.read_text().replace('"', '\\"')
    await redis_client.function_load(function_source, replace=True)
    logger.info(f'Function {func_name} installed')


class CacheTypes(enum.StrEnum):
    Credential = 'cred'
    Token = 'tk'
    OutstockDoc = 'STOA'

def get_cache_key(field: str, cache_type: CacheTypes, vendor: VendorTypes):
    return f'{cache_type}:{vendor}:{field}'

def get_credential_key(ctype: VendorTypes, uid: str):
    return f'{CacheTypes.Credential}:{ctype}:{uid}'

async def get_val(key: str):
    val = await redis_client.get(key)
    if val and val.startswith('{'):
        val = json.loads(val)
    return val

async def set_val(key: str, val: any, **kwargs):
    val = json.dumps(val) if not isinstance(val, str) else val
    return await redis_client.set(key, val, **kwargs)

async def del_val(key: str):
    return await redis_client.delete(key)

async def del_pattern(pattern: str):
    deleted_count = await redis_client.fcall('del_pattern', 0, pattern)
    logger.info(f'Deleted pattern {pattern}: {deleted_count}')
    return deleted_count

async def mget_val(*keys: str):
    vals = await redis_client.mget(*keys)
    for i, val in enumerate(vals):
        if val and val.startswith('{'):
            vals[i] = json.loads(val)
    return vals

def redis_cache(prefix: str, enabled = True, **cache_kwargs):
    """异步 Redis 缓存装饰器"""
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            if not enabled:
                return await func(*args, **kwargs)
            # 生成缓存键
            key_parts = [prefix]
            key_parts.extend([str(arg) for arg in args])
            for k, v in sorted(kwargs.items()):
                key_parts.append(f"{k}={v}")
            cache_key = ":".join(key_parts)

            try:
                # 异步获取缓存
                cached_data = await get_val(cache_key)
                if cached_data is not None:
                    return cached_data
            except Exception as e:
                logger.error(f"redis_cache read error: {e}")

            # 执行原函数
            result = await func(*args, **kwargs)

            try:
                # 异步写入缓存
                await set_val(cache_key, result, **cache_kwargs)
            except Exception as e:
                logger.error(f"redis_cache write error: {e}")

            return result

        return wrapper

    return decorator
