import logging
import time
from typing import List, Callable, Awaitable

from httpx import Request, Response, AsyncBaseTransport, AsyncHTTPTransport

logger = logging.getLogger(__name__)

class RetryLoginTransport(AsyncBaseTransport):
    def __init__(
        self,
        max_retries: int = 1,
        retry_status_codes: List[int] = None,
        request_hooks: List[Callable[[Request], Awaitable[None]]] = None
    ):
        self.max_retries = max_retries
        self.retry_status_codes = retry_status_codes or []
        self.request_hooks = request_hooks or []
        self._transport = AsyncHTTPTransport()

    async def handle_async_request(self, request: Request) -> Response:
        start_time = time.time()
        for retry in range(self.max_retries + 1):
            # Re-execute hooks on retry attempts (hooks are already executed on first attempt by httpx)
            if retry > 0:
                for hook in self.request_hooks:
                    await hook(request)

            response = await self._transport.handle_async_request(request)

            if retry < self.max_retries:
                should_retry = await self._should_retry(response)
                if should_retry:
                    await self.before_retry(request)
                    logger.info(f"Retrying request {retry+1}/{self.max_retries} {request.url.path}")
                    continue
            duration = time.time() - start_time
            logger.info(f"{request.url.path} {duration:.3f}s")
            return response
        raise

    async def _should_retry(self, response: Response) -> bool:
        if response.status_code in self.retry_status_codes:
            return True
        if await self.retry_by_response(response):
            return True
        return False

    async def retry_by_response(self, response: Response):
        return False
    async def before_retry(self, request: Request):
        raise NotImplementedError("The 'before_retry' method must be implemented.")