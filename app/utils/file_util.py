import hashlib
from pathlib import Path

from s3path import S3Path

from app.utils.app_config import app_config


def ensure_dir_for_file(file_path: Path):
    dir_name = file_path.parent
    if not dir_name.exists():
        dir_name.mkdir(parents=True)

# fp.write_text('Text file contents')
# fp.read_text()
# fp.write_bytes()
# fp.read_bytes()

def get_file_hash(file_path: Path, alg = 'sha256'):
    hash_func = hashlib.new(alg)
    with file_path.open("rb") as f:
        while chunk := f.read(65536):
            hash_func.update(chunk)
    return hash_func.hexdigest()

_browser_preloads_prefix = f'/{app_config.cache_bucket}/browser_preloads'
def create_reroute_maps(origin: str, presets: dict = None):
    host = origin.split('//')[1]
    s3path = S3Path(f'{_browser_preloads_prefix}/{host}')
    reroute_maps = {}
    for x in s3path.glob('**'):
        if not x.is_file():
            continue
        short_path = str(x.relative_to(s3path))
        reroute_maps[origin + '/' + short_path] = x
    if presets:
        for k, v in presets.items():
            reroute_maps[origin + '/' + k] = S3Path(f'{_browser_preloads_prefix}/{v}')
    return reroute_maps