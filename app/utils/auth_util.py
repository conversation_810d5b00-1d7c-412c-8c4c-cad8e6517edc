
from typing import Optional, Dict, Any, Tuple
from datetime import datetime, timedelta, timezone
from fastapi import HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import jwt, JWTError, ExpiredSignatureError
from pydantic import BaseModel

from app.utils.app_config import app_config

# 认证配置
SECRET_KEY = app_config.jwt_secret
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 24 * 60
REFRESH_TOKEN_EXPIRE_MINUTES = 24 * 60 * 7


class UserInfo(BaseModel):
    """用户信息模型"""
    user_id: int
    name: str


def create_token(data: dict, expires_delta: timedelta) -> str:
    """
    创建JWT token
    
    Args:
        data: 要编码的数据
        expires_delta: 过期时间
        
    Returns:
        JWT token字符串
    """
    to_encode = data.copy()
    expire = datetime.now(timezone.utc) + expires_delta
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def verify_token(token: str) -> Dict[str, Any]:
    """
    验证JWT token
    
    Args:
        token: JWT token字符串
        
    Returns:
        解码后的payload，如果验证失败返回None
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail='Token 已过期'
        )
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail='无效的 Token'
        )


def create_access_token(data: dict) -> str:
    return create_token(data, timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES))


def create_refresh_token(data: dict) -> str:
    return create_token(data, timedelta(minutes=REFRESH_TOKEN_EXPIRE_MINUTES))



get_auth_credentials = HTTPBearer()

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(get_auth_credentials)) -> UserInfo:
    """
    获取当前用户信息
    
    Args:
        credentials: HTTP认证凭据
        
    Returns:
        用户信息
        
    Raises:
        HTTPException: 当认证失败时
    """
    token = credentials.credentials
    payload = verify_token(token)
    return UserInfo(**payload)



