from enum import StrEnum

from pydantic import BaseModel


class KDFilterCompare(StrEnum):
    Equal = '67'
    In = '338'
    EqualField = '24'
    EqualBool = '29'

class KDFilterCondition(BaseModel):
    Left: str = ""
    FieldName: str
    Compare: KDFilterCompare = KDFilterCompare.Equal
    Value: str
    Right: str = ""
    Logic: int = 0

class KDFormIds(StrEnum):
    Material = 'BD_MATERIAL'
    Customer = 'BD_Customer'
    SaleOrder = 'SAL_SaleOrder'
    DeliveryNotice = 'SAL_DELIVERYNOTICE'
    Operator = 'BD_OPERATOR' # 业务员
