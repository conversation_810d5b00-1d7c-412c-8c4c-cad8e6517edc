
import polars as pl

from app.utils.app_config import app_config
from app.utils.date_util import strpdate
from .delivery_notice_strategy import DeliveryNoticeStrategy, DeliveryNoticeFactory


class ZhiWei006Strategy(DeliveryNoticeStrategy):
    @property
    def column_mapping(self) -> dict:
        return {
            '发货日期': '发货日期',
            '工厂': '工厂',
            'F_PYZU_KHWLH': '客户物料号',
            'total_pcs': '数量',
            '备注': '备注',
            '供应商': '供应商',
            'OutstockNo': '发货通知单号',
        }

    @property
    def excel_path(self):
        delivery_date = strpdate(self.outstock_info['F_PYZU_SDate'])
        customer_name = self.outstock_info['customer_name'][:7]
        return super().excel_path.with_stem(f'{customer_name}{delivery_date}出货明细')

    async def get_current_df(self) -> pl.DataFrame:
        df = self.get_delivery_notice_df()
        supplier_code = '1000' if self.outstock_info['customer_no'] == 'SZ-K19040022' else '1400'
        delivery_date = strpdate(self.outstock_info['F_PYZU_SDate'])
        note = f'{delivery_date.strftime("%m-%d")} 快递'
        return df.with_columns(
            pl.lit(delivery_date).alias('发货日期'),
            pl.lit('吉利通').alias('供应商'),
            pl.lit(supplier_code).alias('工厂'),
            pl.lit(note).alias('备注'),
            pl.lit(self.outstock_info['BillNo']).alias('OutstockNo'),
        )

    async def get_merged_df(self) -> pl.DataFrame:
        merged_df = pl.concat(self.dfs, how='vertical')
        group_by_cols = ['发货日期', 'F_PYZU_KHWLH']

        acc_cols = ['OutstockNo']
        not_first_cols = group_by_cols + acc_cols
        merged_df = merged_df.group_by(group_by_cols).agg([
            pl.col('Qty').sum().alias('total_pcs'),
            *[pl.col(col).unique().str.join(',').alias(col) for col in acc_cols],
            *[pl.col(col).first().alias(col) for col in merged_df.columns if col not in not_first_cols]
        ]).sort(group_by_cols)
        return merged_df



DeliveryNoticeFactory.register_strategy('SZ-K19040022', ZhiWei006Strategy)
DeliveryNoticeFactory.register_strategy('SZ-K23070041', ZhiWei006Strategy)

