
import polars as pl

from .delivery_notice_strategy import DeliveryNoticeStrategy, DeliveryNoticeFactory


class XingQuDongLiStrategy(DeliveryNoticeStrategy):

    @property
    def column_mapping(self) -> dict:
        return {
            "CusOrderNo": "客户订单号", # 客户订单号
            "CusCargoCode": "客户物料号", # 客户物料号
            "WMSDC_short": "WMSDC", # WMSDC
            "WMSDC_date": "生产日期",  # WMSDC
            "保质期": "保质期", # = WMSDC日期 + 按品牌*年
            "total_pcs": "数量", # = SUM(PCS) BY 客户订单号 + 客户物料号 + WMSDC
            "IndexNo": "箱号", # 箱号
            "KHSXWL.YCXH": "实需原厂型号", # 实需原厂型号
            "KHSXWL.PINGPAI": "品牌", # 品牌
            # "品牌年份": "品牌年份",
            "OutstockNo": "发货通知单号", # 发货通知单号
        }

    async def get_merged_df(self) -> pl.DataFrame:
        df = await super().get_merged_df()

        brand_years = {
            '绿宝石': '3y',
            '三礼': '1y',
        }
        df = df.with_columns([
            pl.col('WMSDC').str.to_date('%Y%m%d').alias('WMSDC_date'),
            pl.col('KHSXWL.PINGPAI').replace_strict(brand_years, default = '2y').alias('品牌年份'),
        ])

        group_by_cols = ['CusCargoCode', 'LotNo', 'DC', 'IndexNo']
        df = df.group_by(group_by_cols).agg([
            pl.col('PCS').sum().alias('total_pcs'),
            pl.col('WMSDC_date').first().dt.strftime('%y%m%d').alias('WMSDC_short'),
            pl.col('WMSDC_date').dt.offset_by(pl.col('品牌年份')).first().alias('保质期'),
            *[pl.col(col).first().alias(col) for col in df.columns if col not in group_by_cols]
        ]).sort(['CusOrderNo', 'CusCargoCode', 'IndexNo'])
        return df


DeliveryNoticeFactory.register_strategy('SZ-K22060041', XingQuDongLiStrategy)

