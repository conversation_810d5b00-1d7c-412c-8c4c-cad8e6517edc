import logging
from datetime import datetime
from pathlib import Path

from httpx import AsyncClient, Response, Request
import polars as pl

from app.database.redis_client import get_val, get_credential_key, set_val, del_val, CacheTypes, get_cache_key, mget_val
from app.schemas.common import VendorTypes
from app.schemas.flows import FileOutput, MessageOutput
from app.utils.httpx_retry_login import RetryLoginTransport
from app.utils.email_util import send_email
from app.utils.pdf_util import pdf_insert_text
from app.utils.play_api import play_api
from .delivery_notice_strategy import DeliveryNoticeStrategy, DeliveryNoticeFactory

logger = logging.getLogger(__name__)

_credential_key = get_cache_key('LOGIN', cache_type=CacheTypes.Credential, vendor=VendorTypes.ChangCheng004)
_token_key = get_cache_key('TOKEN', cache_type=CacheTypes.Token, vendor=VendorTypes.ChangCheng004)
_to_addrs_key = get_credential_key(VendorTypes.General, 'wms-receiver')
_cc_addrs_key = get_credential_key(VendorTypes.ChangCheng004, 'emails')


async def fetch_credential():
    login_credential = await get_val(_credential_key)
    assert login_credential, '长城登录账号未设置！'
    res = await play_api.post('/gwpst/login', json = {
        "credential": login_credential
    })
    logger.info(f'login response: {res.json()}')
    res.raise_for_status()
    res_json = res.json()
    return res_json['value']


async def attach_credential(request: Request):
    token = await get_val(_token_key)
    if not token:
        token = await fetch_credential()
        await set_val(_token_key, token)
        logger.info(f'session refreshed')
    request.headers['api-token'] = token


class ChangChengRetryTransport(RetryLoginTransport):
    async def before_retry(self, request: Request):
        await del_val(_token_key)


__base_url = 'https://srm.gwpst.com/api/srm-purchase-execute/tenant/delivery/header/supplier'

req_client = AsyncClient(
    base_url=__base_url,
    transport= ChangChengRetryTransport( request_hooks=[attach_credential], retry_status_codes=[401] ),
    event_hooks={ 'request': [attach_credential], },
    timeout=120)


class ChangeChengStrategy(DeliveryNoticeStrategy):
    @property
    def column_mapping(self) -> dict:
        return {
            "F_PYZU_KHZL1": "交货明细序号",
            "CusOrderNo": "采购订单号",
            "NoteEntry": "采购订单行号",
            "F_PYZU_KHZL2": "工厂编码",
            "F_PYZU_KHZL3": "收货地点",
            "CusCargoCode": "物料号",
            "BoxSerialNo": "箱号条码",
            "PanSerialNo": "最小产品包装条码",
            "PCS": "最小包装数",
            "PCS2": "实际包装数",
            "today": "送货日期",
            "LotNo": "生产批次",
            "WMSDC_date": "生产日期",
            "OutstockNo": "备注"
        }

    @property
    def email_content(self):
        return {
            'subject': f'长城送货单 {self.outstock_info['BillNo']}',
            'body': f"""
            <html>
          <body>
            <p>{self.outstock_info['BillNo']} {self.outstock_info['customer_name']}</p>
          </body>
        </html>
        """
        }

    async def get_merged_df(self) -> pl.DataFrame:
        df = await super().get_merged_df()
        df = df.with_columns(
            pl.col("PCS").alias("PCS2"),
            pl.lit(datetime.now().date().strftime('%Y/%m/%d')).alias("today"),
            pl.col("WMSDC").str.to_date("%Y%m%d").dt.strftime('%Y/%m/%d').alias("WMSDC_date")
        )
        return df
    async def flow_upload(self, excel_path: Path):
        files = {'excel': excel_path.read_bytes()}
        res_upload = await req_client.post('/import', files=files)
        res_upload.raise_for_status()
        content_type = res_upload.headers.get('content-type', '')
        if 'excel' in content_type:
            error_path = self.excel_path.with_suffix('.error.xlsx')
            error_path.write_bytes(res_upload.content)
            return error_path
        else:
            logger.info('upload', res_upload.json())
        return None

    async def flow_get_asn_no(self):
        res = await req_client.post('/page', json={"page":{"current":1,"size":2},"condition":"and","rules":[]})
        res_json = res.json()
        # print(11, res_json)
        """
        """
        if res_json['code'] == 200:
            record = res_json['data']['records'][0]
            logger.info(f"送货单状态：{record['asnStatus']}, {record['asnStatusStr']}", record)
            if record['asnStatus'] == 'CREATED':
                return record['asnNo'], ''
            # elif record['asnStatus'] == 'CONFIRMED':
            #     return record['asnNo'], ''
            else:
                return '', record['asnStatusStr']
        return '', res.text

    async def flow_send(self, asn_no: str):
        res = await req_client.post('/send', json={"asnNo": asn_no})
        res_json = res.json()
        if res_json['code'] == 200:
            return res_json['msg']
        return ''

    async def flow_get_pdf_file(self, asn_no: str):
        res = await req_client.post('/print', json={
            'asnNo': asn_no
        })
        res.raise_for_status()
        pdf_path = self.excel_path.with_suffix('.raw.pdf')
        pdf_path.write_bytes(res.content)
        return pdf_path

    def flow_add_no_to_pdf(self, pdf_path):
        new_pdf_path = self.excel_path.with_suffix('.pdf')
        pdf_insert_text(pdf_path, new_pdf_path, self.outstock_info['BillNo'], (16, 30))
        pdf_path.unlink()
        return new_pdf_path

    async def run(self):
        excel_file = await self.get_excel()
        error_excel = await self.flow_upload(excel_file.file_path)
        if error_excel:
            return [
                FileOutput(file_path=error_excel),
                MessageOutput(message=f'上传送货单失败', level=4)
            ]
        asn_no, msg = await self.flow_get_asn_no()
        if not asn_no:
            return [
                excel_file,
                MessageOutput(message=f'送货单状态异常：{msg}', level=8)
            ]
        pdf_file = await self.flow_get_pdf_file(asn_no)
        # pdf_file = self.get_cache_file('.pdf')
        pdf_file = self.flow_add_no_to_pdf(pdf_file)
        try:
            to_addrs, cc_addrs = await mget_val(_to_addrs_key, _cc_addrs_key)
            await send_email(
                to_addrs = to_addrs,
                cc_addrs = cc_addrs,
                **self.email_content,
                attachments=[pdf_file]
            )
            await self.flow_send(asn_no)
            return MessageOutput(message='邮件发送成功！')
        except Exception as e:
            logger.error(f"邮件发送失败", exc_info=True)
            return [
                FileOutput(file_path=pdf_file),
                MessageOutput(message=f'邮件发送失败: {e}')
            ]



DeliveryNoticeFactory.register_strategy('SZ-K21040063', ChangeChengStrategy)
DeliveryNoticeFactory.register_strategy('SZ-K24010055', ChangeChengStrategy)
DeliveryNoticeFactory.register_strategy('SZ-K23030036', ChangeChengStrategy)

