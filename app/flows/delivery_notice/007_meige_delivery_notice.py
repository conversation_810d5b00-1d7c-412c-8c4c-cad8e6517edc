
import polars as pl

from app.utils.app_config import app_config
from app.utils.date_util import strpdate
from .delivery_notice_strategy import DeliveryNoticeStrategy, DeliveryNoticeFactory


class MeiGe007Strategy(DeliveryNoticeStrategy):
    @property
    def excel_path(self):
        delivery_date = strpdate(self.outstock_info['F_PYZU_SDate'])
        return super().excel_path.with_stem(f'美格{delivery_date}')

    @property
    def column_mapping(self) -> dict:
        return {
            'customer_name': '客户',
            'F_PYZU_KHDDH': '客户订单号',
            'F_PYZU_KHWLH': '客户物料号',
            'Qty': '销售数量',
            'KHSXWL.YCXH': '型号', # 客户实需原厂型号
            'KHSXWL.Name': '物料名称',
            'F_PYZU_KHZL1': '工厂',
            'CarriageNO': '运输单号',
            'CarriageName': '物流公司',
            'OutstockNo': '发货通知单号',
        }

    async def get_current_df(self) -> pl.DataFrame:
        df = self.get_delivery_notice_df()
        return df.with_columns(
            pl.lit(self.outstock_info['customer_name']).alias('customer_name'),
            pl.lit(self.outstock_info['CarriageNO']).alias('CarriageNO'),
            pl.lit(self.outstock_info['CarriageName']).alias('CarriageName'),
            pl.lit(self.outstock_info['BillNo']).alias('OutstockNo'),
        )

    async def get_merged_df(self) -> pl.DataFrame:
        print(self.dfs)
        merged_df = pl.concat(self.dfs, how='vertical').sort(['OutstockNo', 'F_PYZU_KHDDH'])
        return merged_df


DeliveryNoticeFactory.register_strategy('SZ-K18080062', MeiGe007Strategy)

