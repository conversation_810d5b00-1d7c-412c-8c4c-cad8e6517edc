from datetime import datetime, <PERSON><PERSON><PERSON>
from pathlib import Path

import httpx
import polars as pl
from httpx import Request

from .delivery_notice_strategy import DeliveryNoticeStrategy, DeliveryNoticeFactory
from app.schemas.flows import FileOutput, MessageOutput
from app.utils.app_config import app_config
from openpyxl import load_workbook

from app.utils.date_util import strpdate, date_add
from app.utils.excel_util import copy_row_style


async def attach_auth(request: Request):
    request.headers['Cookie'] = 'mds_erp_admin_token=6096; mds_erp_admin_username=%E6%B7%B1%E5%9C%B3%E5%90%89%E5%88%A9%E9%80%9A; mds_erp_admin_partnerName=%E6%B7%B1%E5%9C%B3%E5%B8%82%E5%90%89%E5%88%A9%E9%80%9A%E7%94%B5%E5%AD%90%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8; mds_erp_admin_account=2SZJLT'

req_client = httpx.AsyncClient(base_url='http://60.217.68.151:82/webapi/api', event_hooks={'request': [attach_auth]}, timeout=120)

class TianLiStrategy(DeliveryNoticeStrategy):
    dependency_cols = ['FIncreaseQty']

    @property
    def column_mapping(self) -> dict:
        return {
            'CusLineNo': '序号',  # 客户订单行号
            'CusOrderNo': '采购单号',  # 客户订单号
            'CusCargoCode': '物料编码',  # 客户物料号
            'WMSDC': '生产日期',  # WMSDC
            'LotNo': 'LOT信息',  # 批次
            'total_pcs': '数量',  # =客户订单号+客户物料号+WMSDC+批次 汇总
            'IndexNo': '箱数',  # 箱号
            '盒/盘数': '盒/盘数',  # =汇总数量/最小包装
            'OutstockNo': '备注',  # 发货通知通知单号
            '保质期限': '保质期限',  # =WMSDC日期+2年
        }

    async def get_merged_df(self) -> pl.DataFrame:
        df = await super().get_merged_df()
        group_by_cols = ['CusOrderNo', 'CusCargoCode', 'WMSDC', 'LotNo', 'IndexNo']
        df = df.group_by(group_by_cols).agg([
            pl.col('PCS').sum().alias('total_pcs'),
            *[pl.col(col).first().alias(col) for col in df.columns if col not in group_by_cols]
        ]).sort(['CusOrderNo'])
        df = df.with_columns(
            [
                (pl.col('total_pcs')/pl.col('FIncreaseQty')).alias('盒/盘数'),
                pl.when(pl.col("WMSDC").is_not_null())
                .then(
                    pl.col("WMSDC").cast(pl.Utf8)
                    .str.to_date("%Y%m%d")
                    .dt.offset_by("2y")
                    .dt.strftime("%Y%m%d")
                )
                .otherwise(pl.lit(""))
                .alias("保质期限")
            ]
        )
        return df
    async def get_excel(self):
        df = await self.process_data()
        template_path = app_config.project_dir / 'res' / 'templates' / 'delivery_notice_02_tianli.xlsx'
        wb = load_workbook(template_path)
        ws = wb['Sheet1']

        # Convert Polars DataFrame to list of lists for openpyxl
        data_rows = df.rows()
        start_row_idx = 5
        for row_idx, row_data in enumerate(data_rows, start=start_row_idx):
            for col_idx, value in enumerate(row_data, start=1):
                ws.cell(row=row_idx, column=col_idx, value=value)
            if row_idx > 0:
                copy_row_style(ws, start_row_idx, row_idx)

        delivery_date = strpdate(self.outstock_info['F_PYZU_SDate'])
        delivery_no = self.outstock_info['CarriageNO']
        max_box_no = df['箱数'].cast(pl.Int64).max()
        ws.cell(2, 9, max_box_no)
        ws.cell(3, 2, str(delivery_date))
        ws.cell(3, 9, str(date_add( delivery_date, days = 2)))
        cell = ws.cell(3, 7, delivery_no)
        cell.number_format = '@'
                
        wb.save(self.excel_path)
    
        return FileOutput(file_path=self.excel_path)

    async def upload(self, excel_path: Path):
        files = {'file': excel_path.read_bytes()}
        res_upload = await req_client.post('/SystemManage/UploadFile', files=files)
        res_upload.raise_for_status()
        file_info = res_upload.json()
        return file_info

    async def run(self):
        excel_file = await self.get_excel()
        file_info = await self.upload(excel_file.file_path)
        res_import = await req_client.get('/PurchaseManage/ImportData', params= {'file': file_info['Value1'], 'operatorname': '深圳吉利通'})
        # {"Code":"101","Message":"请检查单元格的值，不可为空"}
        # {"Message":"出现错误。","ExceptionMessage":"长度不能小于 0。\r\n参数名: length","ExceptionType":"System.ArgumentOutOfRangeException","StackTrace":"   在 System.Web.Http.ApiController.<InvokeActionWithExceptionFilters>d__1.MoveNext()\r\n--- 引发异常的上一位置中堆栈跟踪的末尾 ---\r\n   在 System.Runtime.CompilerServices.TaskAwaiter.ThrowForNonSuccess(Task task)\r\n   在 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)\r\n   在 System.Web.Http.Dispatcher.HttpControllerDispatcher.<SendAsync>d__0.MoveNext()"}
        # { "Code": "100", "Message": "导入成功！" }
        return MessageOutput(message=res_import.text)


DeliveryNoticeFactory.register_strategy('SZ-*********', TianLiStrategy)
