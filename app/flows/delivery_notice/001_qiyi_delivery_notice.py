
import polars as pl
from fastapi import HTTPException

from app.database.redis_client import get_val, get_credential_key
from app.schemas.common import VendorTypes
from app.utils.play_api import play_api
from .delivery_notice_strategy import DeliveryNoticeStrategy, DeliveryNoticeFactory


class QiYiStrategy(DeliveryNoticeStrategy):
    save_remote = True

    @property
    def column_mapping(self) -> dict:
        return {
            '供应商代码': '供应商代码',
            'CusOrderNo': '采购单号',
            'CusCargoCode': '物料编码',
            'BoxSerialNo': '包装箱号',
            'PanSerialNo': '物料条码',
            'PCS': '物料条码数量',
            'WMSDC': '生产日期',
            'LotNo': '批次号',
            'OutstockNo': '发货通知单号',
        }

    async def get_merged_df(self) -> pl.DataFrame:
        df = await super().get_merged_df()
        supplier_code = 'JLTO' if self.outstock_info['customer_no'] == 'SZ-K18050030' else 'VW_XAYU'
        return df.with_columns(
            pl.lit(supplier_code).alias('供应商代码')
        )

    async def run(self):
        excel_info = await self.get_excel()
        customer_no = self.outstock_info['customer_no']
        credential = await get_val(get_credential_key(VendorTypes.QiYi, customer_no))
        if not credential:
            raise HTTPException(status_code=400, detail=f'请先配置启益登录账号: {customer_no}')
        return await play_api.post('/other/qiyi/delivery_notice', json = {
            'outstock_info': self.outstock_info,
            'credential': credential,
            'file': excel_info.model_dump(),
        })


DeliveryNoticeFactory.register_strategy('SZ-K18050030', QiYiStrategy)
DeliveryNoticeFactory.register_strategy('XY-K24070001', QiYiStrategy)

