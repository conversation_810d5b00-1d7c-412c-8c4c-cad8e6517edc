import openpyxl.styles
import polars as pl
from openpyxl import load_workbook

from .delivery_notice_strategy import DeliveryNoticeStrategy, DeliveryNoticeFactory
from app.schemas.flows import FileOutput
from app.utils.app_config import app_config
from app.utils.date_util import strpdate
from app.utils.excel_util import copy_row_style


class WanKeZiStrategy(DeliveryNoticeStrategy):
    @property
    def column_mapping(self) -> dict:
        return {
            'index': '序号', #
            'KHSXWL.Name': '名称',  # 实需物料名称
            'CusCargoCode': '客户料号', # 客户物料号
            'KHSXWL.YCXH': '型号', # 实需原厂型号
            'KHSXWL.PINGPAI': '品牌', # 品牌
            'LotNo': '批次号', # 批次
            'DC': '周期', # DC
            'total_pcs': '数量', # "=  客户料号+批次号+周期  汇总"
            '判定结果': '判定结果', #
        }

    async def get_merged_df(self) -> pl.DataFrame:
        df = await super().get_merged_df()
        group_by_cols = ['CusCargoCode', 'LotNo', 'DC']
        df = df.group_by(group_by_cols).agg([
            pl.col('PCS').sum().alias('total_pcs'),
            pl.lit("PASS").alias('判定结果'),
            *[pl.col(col).first().alias(col) for col in df.columns if col not in group_by_cols]
        ]).sort('CusCargoCode')
        df = df.with_row_index( offset= 1)
        return df

    async def get_excel(self):
        df = await self.process_data()
        template_path = app_config.project_dir / 'res' / 'templates' / 'delivery_notice_03_wankezi.xlsx'
        wb = load_workbook(template_path)
        ws = wb['Sheet1']

        # Convert Polars DataFrame to list of lists for openpyxl
        data_rows = df.rows()
        start_row_idx = 6
        for row_idx, row_data in enumerate(data_rows, start=start_row_idx):
            for col_idx, value in enumerate(row_data, start=1):
                ws.cell(row=row_idx, column=col_idx, value=value)
            if row_idx > 0:
                copy_row_style(ws, start_row_idx, row_idx)

        delivery_date = strpdate(self.outstock_info['F_PYZU_SDate'])
        ws.cell(3, 1, f"TO：{self.outstock_info['customer_name'].replace('（阻容感）', '')}")
        ws.cell(4, 7, f"报告编号：{self.outstock_info['BillNo']}")
        ws.append([	'', '检验/日期：',f'段争林 {delivery_date}','', '', '', '审核/日期：',	f'胡伟伟 {delivery_date}'])
        max_row = ws.max_row
        ws.row_dimensions[max_row] = ws.row_dimensions[max_row-1]
        for col_idx in range(1, 9):
            ws.cell(max_row, col_idx).alignment = openpyxl.styles.Alignment(horizontal='center', vertical='center')
        ws.merge_cells(f'C{max_row}:D{max_row}')
        ws.merge_cells(f'H{max_row}:I{max_row}')

        wb.save(self.excel_path)

        return FileOutput(file_path=self.excel_path)


DeliveryNoticeFactory.register_strategy('SZ-K25010043', WanKeZiStrategy)
DeliveryNoticeFactory.register_strategy('SZ-K25010044', WanKeZiStrategy)

