import logging
from io import Bytes<PERSON>
from pathlib import Path

from s3path import S3Path

from app.utils.file_util import ensure_dir_for_file

import openpyxl
import polars as pl

from app.routers.kingd import form_query, KDQueryModel
from app.utils.kingd_util import KDFilterCondition, KDFilterCompare

logger = logging.getLogger(__name__)

_form_maps = {
    '物料': 'BD_MATERIAL',
    '客户': 'BD_Customer',
}
def get_form_id(form_name: str):
    form_id = _form_maps.get(form_name)
    if not form_id:
        raise ValueError(f'不支持的单据: {form_name}')
    return form_id

async def get_info(form_id: str, field_keys: list[str], ids: list[str]):
    logger.info(f'Query {form_id} {field_keys} for {ids[:10]}...')
    ids = ','.join(ids)
    filters = [
            KDFilterCondition(FieldName=field_keys[0], Compare=KDFilterCompare.In, Value=ids),
        ]
    if form_id == 'BD_MATERIAL':
        filters.append(
            KDFilterCondition(FieldName='FUseOrgId.FNumber', Compare=KDFilterCompare.Equal, Value="SZ"))
    elif form_id == 'BD_Customer':
        filters.append(
            KDFilterCondition(FieldName='FUseOrgId.FNumber', Compare=KDFilterCompare.EqualField, Value="FCreateOrgId.FNumber"))
    return await form_query(KDQueryModel(
        FormId=form_id,
        FieldKeys=','.join(field_keys),
        FilterString=filters,
        Limit=1000
    ))


async def fill_columns(input_path: Path, output_path: Path = None):
    if not output_path:
        output_path = input_path
    if isinstance(input_path, S3Path):
        input_path = BytesIO(input_path.read_bytes())
    wb = openpyxl.load_workbook(input_path)
    ws = wb.active
    # read the first row
    max_col = ws.max_column
    condition_key = ('', 1)
    query_keys = []
    query_idxes = []
    form_name = ''
    for col_idx in range(1, max_col + 1):
        cell_value = ws.cell(row=1, column=col_idx).value
        if not cell_value:
            continue
        if cell_value.endswith('='):
            query_keys.append(cell_value.replace('=', ''))
            query_idxes.append(col_idx)
        else:
            form_name, cell_value = cell_value.split('.')
            condition_key = (cell_value, col_idx)
    if not (form_name and query_keys and condition_key[0]):
        raise ValueError('No query keys or condition key found')
    form_id = get_form_id(form_name)
    from_row = 3
    batch_size = 800
    max_row = ws.max_row
    logger.info(f'Fill {query_keys} BY {condition_key[0]}')
    for batch_start in range(from_row, max_row + 1, batch_size):
        batch_end = min(batch_start + batch_size - 1, max_row)
        logger.info(f'Process {batch_start-from_row+1}-{batch_end-from_row+1}/{max_row-from_row+1}')
        ids = []
        df = pl.DataFrame()
        for row_idx, row in enumerate(ws.iter_rows(min_row=batch_start, max_row=batch_end, min_col=condition_key[1], max_col=condition_key[1], values_only=True), start = batch_start):
            if row[0]:
                df = df.vstack(pl.DataFrame({'row_idx': row_idx, condition_key[0]: row[0]}))
                ids.append(row[0])
        if not ids:
            continue
        info_list = await get_info(form_id, [condition_key[0]] + query_keys, ids)
        df3 = pl.from_records(info_list)

        # Create rename mapping for query_keys: replace "_" with "." in df3 columns
        rename_mapping = {}
        for key in [condition_key[0]] + query_keys:
            # Check if the key with "_" replaced by "." exists in df3 columns
            dot_version = key.replace('_', '.')
            if dot_version in df3.columns:
                rename_mapping[dot_version] = key
        
        if rename_mapping:
            df3 = df3.rename(rename_mapping)

        df = df.join(df3, left_on=condition_key[0], right_on=condition_key[0], how='left')
        for row in df.iter_rows(named=True):
            for idx, key in enumerate(query_keys):
                ws.cell(row=row['row_idx'], column=query_idxes[idx]).value = row[key]
        # break

    ensure_dir_for_file(output_path)
    wb.save(output_path)


