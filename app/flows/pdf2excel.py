
from fastapi import HTTPException
from io import String<PERSON>
from pathlib import Path

import pandas as pd
import polars as pl
import markdown
from xlsxwriter import Workbook

from app.schemas.flows import FileOutput
from app.utils.app_config import app_config
from app.utils.excel_util import estimate_row_height
from app.utils.markdown_util import pdf2md, split_table


def html2df(html: str, headers: list[str], markdown_text: str) -> pd.DataFrame:
    converters = {}
    for head in headers:
        if '编码' in head:
            converters[head] = str
    if '中国长城科技集团股份有限公司' in markdown_text:
        converters['项目'] = str
    print('converters', converters)
    dfs = pd.read_html(StringIO(html), converters=converters)
    df = pd.concat(dfs)
    return df

def md2excel(markdown_text, output_path):
    html = markdown.markdown(markdown_text, extensions=["tables"])
    tables, plist = split_table(html)

    if len(tables) > 1:
        headers1 = tables[0].find_all('tr')[0]
        headers2 = tables[1].find_all('tr')[0]
        headers = [th.get_text(separator=' ', strip=True) for th in headers1.find_all('th')]
        # headers not the same, treat as the following table no headers
        if headers1.text != headers2.text:
            for idx, tb in enumerate(tables[1:]):
                for row in tb.find_all('tr'):
                    tables[0].append(row)
            tables[1:] = [] # only keep the first table
    else:
        headers = [th.get_text(separator=' ', strip=True) for th in tables[0].find_all('tr')[0].find_all('th')]


    table_html = '\n'.join([str(tb) for tb in tables])
    df = html2df(table_html, headers, markdown_text)
    # write_text_file(output_path.replace('.xlsx', '.html'), markdown_text)

    records = df.to_dict("records")
    print(records[0])
    schema_overrides = {}
    # if '物料编码' in df.columns:
        # schema_overrides['物料编码'] = pl.Utf8

    # convert to polars DataFrame cause it support autofit
    df = pl.from_records(records, schema_overrides=schema_overrides)
    if '深圳市宝龙海能达科技有限公司' in markdown_text:
        df = df.with_columns(
            pl.col('部件号').str.replace_all(' ', '', literal=True),
            pl.col('交货日期').str.to_date('%d-%m-%y')
        )
    elif '湖南维胜科技有限公司' in markdown_text:
        df = df.with_columns(
            pl.col('物料编号').str.replace_all(' ', '', literal=True),
        )



    with Workbook(output_path, {'nan_inf_to_errors': True}) as wb:
        ws = wb.add_worksheet("Sheet1")
        
        # Create number format to hide trailing zeros and show thousand separators
        number_format = wb.add_format({'num_format': '#,##0.######'})
        
        df.write_excel(
            workbook= wb,
            worksheet= ws,
            autofit=True,
            row_heights = 24,
            float_precision = 6
        )
        
        for col_idx, col_name in enumerate(df.columns):
            # print(col_name, df[col_name].dtype)
            # 1. format to numeric columns to hide trailing zeros
            if df[col_name].dtype in [pl.Float32, pl.Float64, pl.Int32, pl.Int64]:
                for row in range(len(df)):
                    v = df[row, col_name]
                    if not pd.isna(v) and v != "NaN":
                        ws.write_number(row + 1, col_idx, v, number_format)
            # 2. replace NaN to empty
            for row in range(len(df)):
                v = df[row, col_name]
                if pd.isna(v) or v == "NaN":
                    ws.write_string(row + 1, col_idx, "")

        current_row = len(df) + 1 + 2
        max_column = len(df.columns) - 1
        text_format = wb.add_format({
            'text_wrap': True,
            'valign': 'vcenter',
            'font_size': 12,
        })

        for (tag, text) in plist:
            estimate_height = estimate_row_height(text)
            ws.set_row(current_row, estimate_height)
            ws.merge_range(current_row, 0, current_row, max_column, text, text_format)
            current_row += 1

async def pdf2excel(file: FileOutput):
    if not file.file_path.is_file():
        raise HTTPException(status_code=400, detail = f'File `{file.file_key}` not exist')
    md_text = pdf2md(file.file_path)
    if not md_text:
        raise HTTPException(status_code=400, detail = f'OCR 服务未启用！')
    excel_path = file.file_path.with_suffix(".xlsx")
    md2excel(md_text, excel_path)
    return excel_path, f'{Path(file.filename).stem}.xlsx'


