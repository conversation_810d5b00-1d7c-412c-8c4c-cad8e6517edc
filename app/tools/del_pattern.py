import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

import asyncio

from app.database.redis_client import del_pattern
from app.utils.app_config import app_config


async def main():
    pattern = '' or input(f'Redis Url: {app_config.redis_url}\nPlease input keys pattern to delete:\n')
    if not pattern:
        return
    await del_pattern(pattern)

if __name__ == '__main__':
    asyncio.run(main())