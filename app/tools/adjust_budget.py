import asyncio
import logging

import openpyxl

from app.routers.kingd import form_query, KDQueryModel
from app.utils.kingd_util import KDFilterCondition, KDFilterCompare, KDFormIds

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

_budget_year = '2025-01-01'
_admin = {
    'no': '2503867',
    'name': '杨光'
}

async def get_sales_no(sales_name):
    sales = await form_query(KDQueryModel(
        FormId= KDFormIds.Operator,
        FieldKeys='FNumber, FName',
        FilterString=[
            KDFilterCondition(FieldName='FName', Compare=KDFilterCompare.Equal, Value=sales_name),
            KDFilterCondition(FieldName='FForbiddenStatus', Compare=KDFilterCompare.EqualBool, Value="0"),
            KDFilterCondition(FieldName='FBizOrgId.FNumber', Compare=KDFilterCompare.Equal, Value="SZ")
        ],
        Limit=1000
    ))
    assert len(sales) == 1, f'查询结果 {sales}'
    return sales[0]['FNumber']



def adjust_budget(excel_file: str, sales_no: str, sales_name: str):
    wb = openpyxl.load_workbook(excel_file)
    ws = wb.active

    header = [cell.value for cell in ws[1]]

    # F_PYZU_Date
    # F_PYZU_YWY
    # F_PYZU_YWY#Name
    # FCreatorId
    # FCreatorId#Name
    # F_sqkj_ModifierId
    # F_sqkj_ModifierId#Name
    update_cols = {
        'F_PYZU_Date': _budget_year,
        'F_PYZU_YWY': sales_no,
        'F_PYZU_YWY#Name': sales_name,
        'FCreatorId': _admin['no'],
        'FCreatorId#Name': _admin['name'],
        'F_sqkj_ModifierId': _admin['no'],
        'F_sqkj_ModifierId#Name': _admin['name'],
    }
    update_configs = [
        {'col_idx': header.index(col_id), 'value': v, 'col_id': col_id}
        for col_id, v in update_cols.items()
    ]

    # 从第3行开始修改（openpyxl是1-based）
    for row in ws.iter_rows(min_row=3, max_row=ws.max_row):
        for col in update_configs:
            row[col['col_idx']].value = col['value']

    wb.save(excel_file)
    logger.info(f"{ws.max_row-2} rows updated")

async def main():
    input_excel = '' or input('请输入导出的预算明细文件路径：\n')
    input_sales_name = '' or input('请输入要调整的业务员名称：\n')
    if not (input_excel and input_sales_name):
        return
    sales_no = await get_sales_no(input_sales_name)
    adjust_budget(input_excel, sales_no, input_sales_name)
    input("""1. 导入前请先反审核相应物料
2. 导入后请审核相应物料
Press <Enter> to exit.""")

if __name__ == '__main__':
    asyncio.run(main())