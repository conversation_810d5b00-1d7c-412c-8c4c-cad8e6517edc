import logging

logging.basicConfig(level=logging.INFO)

# load aws credentials from .env
import dotenv
dotenv.load_dotenv()

from contextlib import asynccontextmanager

from .database.redis_client import redis_client, install_function

from .utils.exception_util import set_exception_handler

from fastapi import FastAPI

from .routers import flows, files, kingd, users
from fastapi.middleware.cors import CORSMiddleware






@asynccontextmanager
async def lifespan(app: FastAPI):
    # 确保 Redis 连接可用
    await redis_client.ping()
    await install_function('del_pattern')

    yield

    await redis_client.aclose()
app = FastAPI(lifespan=lifespan)


app.add_middleware(CORSMiddleware, allow_origins=["*"], allow_methods=["*"], allow_headers=["*"], expose_headers=["*"], max_age=86400)


set_exception_handler(app)

app.include_router(flows.router, prefix="/flows", tags=["Flow"])

app.include_router(files.router, prefix="/files", tags=["Files"])

app.include_router(kingd.router, prefix="/kingd", tags=["Kingdee"])

app.include_router(users.router, prefix="/users", tags=["Users"])