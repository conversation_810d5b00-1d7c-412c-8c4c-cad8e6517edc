from concurrent.futures import ThreadPoolExecutor
from httpx import AsyncClient
import asyncio

client = AsyncClient()


async def fetch_url(url):
    response = await client.get(url, timeout=5)
    return (url, response.status_code)


urls = [
    "https://www.example.com",
    "https://www.github.com",
    "https://www.python.org"
]


async def main():
    with ThreadPoolExecutor(max_workers=3) as executor:
        tasks = [fetch_url(url) for url in urls]
        results = await asyncio.gather(*tasks)

        # 打印结果
        for url, status in results:
            print(f"{url} → 状态码：{status}")


asyncio.run(main())