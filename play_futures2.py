from concurrent import futures
import httpx

def fetch_url(url):
    response = httpx.get(url, timeout=5)
    return (url, response.status_code)

urls = [
    "https://www.example.com",
    "https://www.github.com",
    "https://www.python.org"
]

with futures.ThreadPoolExecutor(max_workers=3) as executor:
    results = executor.map(fetch_url, urls)
    for url, status in results:
        print(f"{url} → 状态码：{status}")
