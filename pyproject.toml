[project]
name = "fast-flow"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "beautifulsoup4>=4.13.4",
    "boto3>=1.38.35",
    "fastapi[standard]>=0.116.1",
    "httpx>=0.28.1",
    "jmespath>=1.0.1",
    "lxml>=5.4.0",
    "markdown>=3.8",
    "openpyxl>=3.1.5",
    "pandas>=2.3.0",
    "pillow>=11.3.0",
    "polars>=1.30.0",
    "pydantic-settings>=2.10.1",
    "pymupdf4llm>=0.0.24",
    "python-dateutil>=2.8.2",
    "python-jose[cryptography]>=3.5.0",
    "python-multipart>=0.0.20",
    "redis>=6.2.0",
    "s3path>=0.6.4",
    "xlsxwriter>=3.2.3",
]

[[tool.uv.index]]
url = "https://mirrors.aliyun.com/pypi/simple"
default = true

[dependency-groups]
dev = [
    "pytest>=8.4.1",
    "pytest-asyncio>=1.0.0",
]
