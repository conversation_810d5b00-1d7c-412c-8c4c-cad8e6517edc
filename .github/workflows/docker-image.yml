name: Docker Image CI

on:
  push:
    branches: [ "main" ]

jobs:

  build:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
    - name: Login to Aliyun Container Registry
      uses: docker/login-action@v3
      with:
        registry: registry.cn-shenzhen.aliyuncs.com
        username: imbrook
        password: ${{ secrets.ALI_DOCKER_TOKEN }}
    - name: Build the Docker image
      run: |
        sudo timedatectl set-timezone Asia/Shanghai
        tag_path=registry.cn-shenzhen.aliyuncs.com/jeelyton/fast-flow
        tag_with_version=$tag_path:$(date +"%y%m.%H%M")
        tag_latest=$tag_path:latest
        docker build . --file deploy/Dockerfile --tag $tag_with_version
        docker push $tag_with_version
        docker tag $tag_with_version $tag_latest
        docker push $tag_latest
