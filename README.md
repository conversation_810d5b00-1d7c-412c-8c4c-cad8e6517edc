```sh
docker run -d \
    --name fast-flow \
    --network local \
    -p 8601:80 \
    -v ./.env:/app/.env:ro \
    -v fast_flow_cache:/app/.venv/jcache \
      --label "traefik.enable=true" \
      --label "traefik.http.routers.fast-flow.rule=Host(\`flow.17ch.cn\`)" \
      --label "traefik.http.routers.fast-flow.entrypoints=websecure" \
      --label "traefik.http.routers.fast-flow.tls.certresolver=letsencrypt" \
    --restart=unless-stopped \
    --add-host=host.docker.internal:host-gateway \
    registry.cn-shenzhen.aliyuncs.com/jeelyton/fast-flow
```

```sh
rclone sync --progress --exclude ".DS_Store" --delete-after  ./tests/inputs/ minio:xconfig/tests/
```