FROM registry.cn-shenzhen.aliyuncs.com/lib0/python:3.13-bookworm


COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/



WORKDIR /app

# Install dependencies
RUN --mount=type=cache,target=/root/.cache/uv \
    --mount=type=bind,source=uv.lock,target=uv.lock \
    --mount=type=bind,source=pyproject.toml,target=pyproject.toml \
    uv sync --frozen --no-install-project

COPY . .

# Sync the project
RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --frozen

EXPOSE 80

CMD ["/app/.venv/bin/fastapi", "run", "app/main.py", "--proxy-headers", "--host", "0.0.0.0", "--port", "80"]

# docker build --platform=linux/amd64  -t registry.cn-shenzhen.aliyuncs.com/jeelyton/fast-flow .