
services:
  fast-flow:
    image: registry.cn-shenzhen.aliyuncs.com/jeelyton/fast-flow
    container_name: fast-flow
    restart: unless-stopped
    ports:
      - "8601:80"
    networks:
      - local
    volumes:
      - ./.env:/app/.env:ro
      - cache:/app/.venv/jcache
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.fast-flow.rule=Host(`flow.17ch.cn`)"
      - "traefik.http.routers.fast-flow.entrypoints=websecure"
      - "traefik.http.routers.fast-flow.tls.certresolver=letsencrypt"
    extra_hosts:
      - "host.docker.internal:host-gateway"

networks:
  local:
    external: true

volumes:
  cache: